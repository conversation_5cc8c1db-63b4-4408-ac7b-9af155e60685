<template>
  <section id="tech" class="tech-section">
    <div class="container">
      <div class="section-header fade-in">
        <h2 class="section-title">我的技能</h2>
        <p class="section-description">
          这些是我目前掌握的技术，还在不断学习中
        </p>
      </div>

      <div class="skills-content">
        <div class="skills-grid">
          <div
            class="skill-item slide-in-right"
            v-for="(skill, index) in contentStore.skills"
            :key="skill.name"
            :class="`stagger-${index + 1}`"
          >
            <div class="skill-info">
              <span class="skill-name">{{ skill.name }}</span>
              <span class="skill-level">{{ skill.level }}%</span>
            </div>
            <div class="skill-bar">
              <div
                class="skill-progress glow"
                :style="{ width: skill.level + '%' }"
                :class="skill.color"
              ></div>
            </div>
          </div>
        </div>

        <div class="learning-stats slide-in-left stagger-5">
          <div class="stat-card card scale-in stagger-1">
            <div class="stat-number">4</div>
            <div class="stat-label">掌握技能</div>
          </div>

          <div class="stat-card card scale-in stagger-2">
            <div class="stat-number">10+</div>
            <div class="stat-label">小项目</div>
          </div>

          <div class="stat-card card scale-in stagger-3">
            <div class="stat-number">∞</div>
            <div class="stat-label">学习热情</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { useContentStore } from '@/stores/content'

const contentStore = useContentStore()
</script>

<style scoped>
.tech-section {
  padding: 6rem 0;
}

/* 当作为网格子项时的样式 */
.skills-container .tech-section {
  padding: 4rem 2rem;
  margin: 0;
  background: transparent;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 800;
}

.section-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.skills-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
}

.skills-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.skill-item {
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 1.5rem;
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.skill-item:hover {
  transform: translate3d(0, -3px, 0);
  box-shadow: var(--shadow-lg);
  border-color: rgba(255, 255, 255, 0.4);
}

.skill-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.skill-name {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.skill-level {
  font-size: 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

.skill-bar {
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 1.5s ease-in-out;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

.skill-scratch { background: linear-gradient(90deg, #f7df1e, #f0db4f); }
.skill-css { background: linear-gradient(90deg, #1572b6, #33a9dc); }
.skill-html { background: linear-gradient(90deg, #e34f26, #f06529); }
.skill-node { background: linear-gradient(90deg, #339933, #68a063); }

.learning-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 2rem 1rem;
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.stat-card:hover {
  transform: translate3d(0, -3px, 0) scale3d(1.03, 1.03, 1);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .skills-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .learning-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .skills-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* 在网格布局中调整技能项 */
  .skills-container .skill-item {
    padding: 1.25rem;
  }

  .learning-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 767px) {
  .tech-section {
    padding: 4rem 0;
  }

  .learning-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem 0.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }
}
</style>
