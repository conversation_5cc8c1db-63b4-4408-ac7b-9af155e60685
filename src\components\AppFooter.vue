<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <!-- 简单的联系信息 -->
        <div class="footer-contact fade-in">
          <div class="contact-item">
            <div class="contact-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
              </svg>
            </div>
            <div @click="handleContactClick">
              <div class="--text-secondary">
                {{ contentStore.personalInfo.email }}
              </div>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </div>
            <a href="https://github.com/Greatgrap" target="_blank">
              GitHub
            </a>
          </div>
        </div>

        <!-- 版权信息 -->
        <div class="footer-copyright fade-in stagger-1">
          <p>&copy; {{ currentYear }} {{ contentStore.personalInfo.name }} - 个人网站</p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useContentStore } from '@/stores/content'

const contentStore = useContentStore()

const currentYear = computed(() => new Date().getFullYear())

// 处理联系我点击事件
const handleContactClick = () => {
  const email = contentStore.personalInfo.email
  alert(`请发邮件到：${email} ，谢谢`)
}
</script>

<style scoped>
.app-footer {
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--border-color);
  padding: 2rem 0;
  margin-top: 4rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
}

/* 联系信息 */
.footer-contact {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-icon {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
}

.contact-icon svg {
  width: 100%;
  height: 100%;
}

.contact-item a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.95rem;
}

.contact-item a:hover {
  color: var(--primary-color);
}

/* 版权信息 */
.footer-copyright p {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .app-footer {
    padding: 1.5rem 0;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-contact {
    justify-content: center;
    gap: 1.5rem;
  }
}
</style>
