<template>
  <header class="app-header glass">
    <div class="container">
      <div class="header-content">
        <!-- Logo/Brand -->
        <div class="brand">
          <RouterLink to="/" class="brand-link">
            <div class="logo-container">
              <div class="brand-logo">
                <span class="logo-icon">G</span>
              </div>
              <div class="brand-info">
                <span class="brand-text">{{ contentStore.personalInfo.company }}</span>
              </div>
            </div>
          </RouterLink>
        </div>

        <!-- Theme Toggle -->
        <div class="header-actions">
          <ThemeToggle />
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { RouterLink } from 'vue-router'
import { useContentStore } from '@/stores/content'
import ThemeToggle from './ThemeToggle.vue'

const contentStore = useContentStore()
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  padding: 0 1rem;
}

/* Brand Styles */
.brand-link {
  text-decoration: none;
  color: inherit;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  color: white;
  font-size: 1.2rem;
  box-shadow: var(--shadow-md);
}

.brand-info {
  display: flex;
  align-items: center;
}

.brand-text {
  font-size: 1.3rem;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
}

/* 删除导航样式 */

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 删除移动端样式 */

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Responsive Styles */
@media (max-width: 767px) {
  .header-content {
    height: 70px;
    padding: 0 1rem;
  }

  .brand-logo {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .brand-text {
    font-size: 1.1rem;
  }

  .logo-container {
    gap: 0.5rem;
  }
}

@media (min-width: 768px) {
  .header-content {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .header-content {
    height: 90px;
  }

  .brand-logo {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .brand-text {
    font-size: 1.4rem;
  }
}
</style>
