// 滚动动画工具 - 性能优化版本
export function initScrollAnimations() {
  // 检查是否支持 Intersection Observer
  if (!('IntersectionObserver' in window)) {
    // 如果不支持，直接显示所有元素
    const animatedElements = document.querySelectorAll(
      '.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in, .bounce-in'
    )
    animatedElements.forEach(el => {
      el.style.opacity = '1'
      el.style.transform = 'none'
    })
    return
  }

  // 使用 requestIdleCallback 优化性能
  const processAnimations = (entries) => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => handleIntersection(entries))
    } else {
      handleIntersection(entries)
    }
  }

  const handleIntersection = (entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        // 元素进入视口时添加动画类
        entry.target.classList.add('animate-in')

        // 停止观察已经动画的元素
        observer.unobserve(entry.target)

        // 如果有延迟动画的子元素，使用更高效的方式触发
        const staggerElements = entry.target.querySelectorAll('[class*="stagger-"]')
        if (staggerElements.length > 0) {
          staggerElements.forEach((el, index) => {
            // 使用 requestAnimationFrame 优化动画时机
            setTimeout(() => {
              requestAnimationFrame(() => {
                el.classList.add('animate-in')
              })
            }, index * 80) // 减少延迟时间
          })
        }
      }
    })
  }

  // 创建 Intersection Observer
  const observer = new IntersectionObserver(processAnimations, {
    threshold: 0.15, // 稍微提高阈值
    rootMargin: '0px 0px -30px 0px' // 减少边距
  })

  // 观察所有需要动画的元素
  const animatedElements = document.querySelectorAll(
    '.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in, .bounce-in'
  )

  animatedElements.forEach((el) => {
    // 初始状态设为不可见
    el.style.opacity = '0'
    el.style.transform = getInitialTransform(el)
    observer.observe(el)
  })
}

// 根据动画类型获取初始变换 - 使用 transform3d 优化
function getInitialTransform(element) {
  if (element.classList.contains('slide-up')) {
    return 'translate3d(0, 30px, 0)'
  } else if (element.classList.contains('slide-in-left')) {
    return 'translate3d(-30px, 0, 0)'
  } else if (element.classList.contains('slide-in-right')) {
    return 'translate3d(30px, 0, 0)'
  } else if (element.classList.contains('scale-in') || element.classList.contains('bounce-in')) {
    return 'scale3d(0.9, 0.9, 1)'
  }
  return 'none'
}

// 平滑滚动到指定元素
export function scrollToElement(elementId, offset = 80) {
  const element = document.getElementById(elementId)
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
    const offsetPosition = elementPosition - offset

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })
  }
}

// 视差滚动效果 - 性能优化版本
export function initParallaxEffect() {
  const parallaxElements = document.querySelectorAll('.parallax')

  if (parallaxElements.length === 0) return () => {}

  let ticking = false

  function updateParallax() {
    const scrolled = window.pageYOffset
    const rate = scrolled * -0.3 // 减少视差强度

    parallaxElements.forEach((element) => {
      element.style.transform = `translate3d(0, ${rate}px, 0)`
    })

    ticking = false
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateParallax)
      ticking = true
    }
  }

  // 使用节流优化滚动性能
  window.addEventListener('scroll', requestTick, { passive: true })

  // 返回清理函数
  return () => {
    window.removeEventListener('scroll', requestTick)
  }
}

// 鼠标跟随效果
export function initMouseFollowEffect() {
  const cursor = document.createElement('div')
  cursor.className = 'custom-cursor'
  cursor.innerHTML = '<div class="cursor-dot"></div><div class="cursor-ring"></div>'
  document.body.appendChild(cursor)

  let mouseX = 0
  let mouseY = 0
  let cursorX = 0
  let cursorY = 0

  document.addEventListener('mousemove', (e) => {
    mouseX = e.clientX
    mouseY = e.clientY
  })

  function animateCursor() {
    const speed = 0.1
    cursorX += (mouseX - cursorX) * speed
    cursorY += (mouseY - cursorY) * speed
    
    cursor.style.left = cursorX + 'px'
    cursor.style.top = cursorY + 'px'
    
    requestAnimationFrame(animateCursor)
  }
  
  animateCursor()

  // 悬停效果
  const hoverElements = document.querySelectorAll('button, a, .card')
  hoverElements.forEach((el) => {
    el.addEventListener('mouseenter', () => {
      cursor.classList.add('cursor-hover')
    })
    el.addEventListener('mouseleave', () => {
      cursor.classList.remove('cursor-hover')
    })
  })
}
