<template>
  <button
    @click="toggleTheme"
    class="theme-toggle"
    :aria-label="isDark ? 'Switch to light mode' : 'Switch to dark mode'">
    <div class="toggle-track">
      <div class="toggle-thumb" :class="{ 'toggle-thumb--dark': isDark }">
        <div class="toggle-icon">
          <svg v-if="themeIcon === 'sun'" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"/>
          </svg>
          <svg v-else viewBox="0 0 24 24" fill="currentColor">
            <path d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"/>
          </svg>
        </div>
      </div>
    </div>
  </button>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useThemeStore } from '@/stores/counter'

const themeStore = useThemeStore()

// 使用storeToRefs确保响应性
const { isDark, themeIcon } = storeToRefs(themeStore)
const { toggleTheme } = themeStore
</script>

<style scoped>
.theme-toggle {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background: var(--bg-tertiary);
  transform: scale(1.05);
}

.toggle-track {
  width: 60px;
  height: 30px;
  background: var(--bg-tertiary);
  border-radius: 15px;
  position: relative;
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
}

.toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background: var(--gradient-primary);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.toggle-thumb--dark {
  transform: translateX(28px);
  background: var(--gradient-secondary);
}

.toggle-icon {
  width: 14px;
  height: 14px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon svg {
  width: 100%;
  height: 100%;
}

/* Hover effects */
.theme-toggle:hover .toggle-track {
  box-shadow: var(--shadow-lg);
}

.theme-toggle:hover .toggle-thumb {
  transform: scale(1.1);
}

.theme-toggle:hover .toggle-thumb--dark {
  transform: translateX(28px) scale(1.1);
}

/* Focus styles for accessibility */
.theme-toggle:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .theme-toggle {
    padding: 0.75rem;
  }
  
  .toggle-track {
    width: 70px;
    height: 36px;
  }
  
  .toggle-thumb {
    width: 28px;
    height: 28px;
  }
  
  .toggle-thumb--dark {
    transform: translateX(32px);
  }
  
  .toggle-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
