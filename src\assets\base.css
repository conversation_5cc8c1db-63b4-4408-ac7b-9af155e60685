/* Modern CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 1rem;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  font: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default list styles */
ul, ol {
  list-style: none;
}

/* Remove default input styles */
input, textarea, select {
  font: inherit;
  border: none;
  outline: none;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2rem, 5vw, 4rem);
  margin-bottom: 1rem;
}

h2 {
  font-size: clamp(1.5rem, 4vw, 3rem);
  margin-bottom: 0.875rem;
}

h3 {
  font-size: clamp(1.25rem, 3vw, 2rem);
  margin-bottom: 0.75rem;
}

h4 {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  margin-bottom: 0.625rem;
}

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
