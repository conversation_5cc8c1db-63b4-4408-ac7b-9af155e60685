<script setup>
import { onMounted, nextTick } from 'vue'
import { RouterView } from 'vue-router'
import { useThemeStore } from '@/stores/counter'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import { initScrollAnimations, initParallaxEffect } from '@/utils/scrollAnimations'

const themeStore = useThemeStore()

onMounted(async () => {
  themeStore.initializeTheme()

  // 等待DOM完全渲染后初始化动画
  await nextTick()

  // 性能检测 - 在低性能设备上减少动画
  const isLowPerformance = checkPerformance()

  setTimeout(() => {
    if (!isLowPerformance) {
      initScrollAnimations()
      initParallaxEffect()
    } else {
      // 低性能设备只启用基础动画
      initBasicAnimations()
    }
  }, 100)
})

// 简单的性能检测
function checkPerformance() {
  // 检查用户偏好
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    return true
  }

  // 检查设备内存（如果支持）
  if ('deviceMemory' in navigator && navigator.deviceMemory < 4) {
    return true
  }

  // 检查硬件并发数
  if ('hardwareConcurrency' in navigator && navigator.hardwareConcurrency < 4) {
    return true
  }

  return false
}

// 基础动画初始化（低性能设备）
function initBasicAnimations() {
  const animatedElements = document.querySelectorAll(
    '.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in, .bounce-in'
  )

  // 直接显示所有元素，不使用动画
  animatedElements.forEach(el => {
    el.style.opacity = '1'
    el.style.transform = 'none'
  })
}
</script>

<template>
  <div id="app">
    <!-- Background with animated gradient -->
    <div class="background-gradient"></div>

    <!-- Main app content -->
    <AppHeader />
    <main class="main-content">
      <RouterView />
    </main>
    <AppFooter />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.background-gradient {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  z-index: -2;
}

.main-content {
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 80px);
}



/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    min-height: calc(100vh - 70px);
  }
}
</style>
