<template>
  <section id="projects" class="projects-section">
    <div class="container">
      <div class="section-header fade-in">
        <h2 class="section-title">我的项目</h2>
        <p class="section-description">
          点击卡片查看我做的一些小项目
        </p>
      </div>

      <div class="projects-grid">
        <div
          class="project-card card scale-in enhanced-hover"
          v-for="(project, index) in contentStore.projects"
          :key="project.id"
          :class="`stagger-${index + 1}`"
          @click="openProject(project.url)"
        >
          <div class="project-icon bounce-in">
            <svg v-if="project.icon === 'game'" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <svg v-else-if="project.icon === 'code'" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
            </svg>
            <svg v-else-if="project.icon === 'blog'" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
            </svg>
          </div>

          <h3 class="project-title">{{ project.title }}</h3>
          <p class="project-description">{{ project.description }}</p>

          <div class="project-tech">
            <span
              v-for="(tech, techIndex) in project.tech"
              :key="techIndex"
              class="tech-tag slide-in-left"
              :class="`stagger-${techIndex + 1}`"
            >
              {{ tech }}
            </span>
          </div>

          <div class="project-action">
            <span class="action-text">点击访问 →</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { useContentStore } from '@/stores/content'

const contentStore = useContentStore()

// 打开项目链接
const openProject = (url) => {
  if (url && url !== '#') {
    window.open(url, '_blank')
  } else {
    // 如果链接为空，显示提示
    alert('项目链接暂未设置，请在代码中更新URL')
  }
}
</script>

<style scoped>
.projects-section {
  padding: 6rem 0;
  background: var(--bg-tertiary);
}

/* 当作为网格子项时的样式 */
.projects-container .projects-section {
  padding: 4rem 2rem;
  margin: 0;
  background: transparent;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 800;
}

.section-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.project-card {
  text-align: center;
  padding: 2.5rem 2rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  will-change: transform;
}

.project-card:hover {
  transform: translate3d(0, -8px, 0) scale3d(1.02, 1.02, 1);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.project-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.project-icon svg {
  width: 100%;
  height: 100%;
}

.project-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 700;
}

.project-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.tech-tag {
  background: var(--bg-glass);
  color: var(--text-primary);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid var(--border-light);
}

.project-action {
  margin-top: auto;
  padding-top: 1rem;
}

.action-text {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.project-card:hover .action-text {
  transform: translate3d(5px, 0, 0);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .projects-section {
    padding: 4rem 0;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .project-card {
    padding: 2rem 1.5rem;
  }

  .project-tech {
    justify-content: flex-start;
  }
}

@media (min-width: 1024px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* 在网格布局中调整项目卡片 */
  .projects-container .project-card {
    padding: 2rem 1.5rem;
  }
}

@media (min-width: 1440px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }
}
</style>
