import { ref, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useContentStore = defineStore('content', () => {
  // 个人信息
  const personalInfo = reactive({
    name: '<PERSON>',
    title: '学生',
    company: 'Greatgrap',
    email: '<EMAIL>',
    avatar: '/images/avatar.jpg',
    greeting: '嗨，我是',
    description: '一名爱折腾的学生',
    bio: '平时喜欢折腾手机、手表和电脑，会写一点HTML、CSS和JavaScript，熟练使用Scratch编程。喜欢学习新技术，偶尔做点小项目玩玩。',
    motto: '学而时习之，不亦说乎'
  })

  // 技能列表
  const skills = ref([
    { name: 'Scratch', level: 90, color: 'skill-scratch' },
    { name: 'CSS/SCSS', level: 70, color: 'skill-css' },
    { name: 'HTML5', level: 75, color: 'skill-html' },
    { name: 'Node.js', level: 65, color: 'skill-node' }
  ])

  // 我的项目
  const projects = ref([
    {
      id: 1,
      title: '五子棋游戏',
      description: '用JavaScript写的在线五子棋游戏',
      icon: 'game',
      url: 'https://gomoku.greatgrap.com',
      tech: ['JavaScript', 'HTML5', 'CSS3']
    },
    {
      id: 2,
      title: 'Scratch作品',
      description: '我在Scratch上创作的各种小游戏和动画',
      icon: 'code',
      url: 'https://scratch.greatgrap.com',
      tech: ['Scratch', '游戏设计', '动画']
    },
    {
      id: 3,
      title: '个人博客',
      description: '记录学习过程和技术心得的地方',
      icon: 'blog',
      url: 'https://blog.greatgrap.com',
      tech: ['写作', '技术分享', '学习笔记']
    }
  ])

  // 社交链接
  const socialLinks = ref([
    { name: 'GitHub', url: 'https://github.com/Greatgrap', icon: 'github' }
  ])

  return {
    personalInfo,
    skills,
    projects,
    socialLinks
  }
})
