@import './base.css';

/* CSS Variables for Theme System */
:root {
  /* Sky Blue Theme Colors */
  --primary-color: #0ea5e9;
  --primary-light: #38bdf8;
  --primary-dark: #0284c7;
  --secondary-color: #06b6d4;
  --accent-color: #3b82f6;

  /* Sky Blue Background - 更稳定的渐变色 */
  --bg-primary: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 30%, #81d4fa 60%, #4fc3f7 100%);
  --bg-tertiary: rgba(255, 255, 255, 0.1);
  --bg-glass: rgba(255, 255, 255, 0.15);
  --bg-card: rgba(255, 255, 255, 0.2);

  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-color: rgba(255, 255, 255, 0.3);
  --border-light: rgba(255, 255, 255, 0.2);

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Gradient Backgrounds */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
}

/* Dark Theme Colors - 更柔和的深色背景 */
[data-theme="dark"] {
  --bg-primary: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 30%, #404040 60%, #525252 100%);
  --bg-secondary: linear-gradient(135deg, #262626 0%, #3f3f3f 30%, #525252 60%, #666666 100%);
  --bg-tertiary: rgba(255, 255, 255, 0.05);
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-card: rgba(255, 255, 255, 0.15);

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;

  --border-color: rgba(255, 255, 255, 0.2);
  --border-light: rgba(255, 255, 255, 0.1);

  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: all 0.3s ease;
  overflow-x: hidden;
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  position: relative;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Glass Morphism Effect */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
}

/* Card Styles with Enhanced Glass Effect */
.card {
  background: var(--bg-card);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid var(--border-color);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0.5;
  z-index: -1;
  border-radius: 24px;
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
}

/* Enhanced Animation Classes - Performance Optimized */
.fade-in {
  animation: fadeIn 0.6s ease-out;
  will-change: opacity;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
  will-change: transform, opacity;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
  will-change: transform, opacity;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
  will-change: transform, opacity;
}

.scale-in {
  animation: scaleIn 0.5s ease-out;
  will-change: transform, opacity;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out;
  will-change: transform, opacity;
}

.float {
  animation: float 4s ease-in-out infinite;
  will-change: transform;
}

.pulse {
  animation: pulse 3s ease-in-out infinite;
  will-change: transform;
}

.glow {
  animation: glow 3s ease-in-out infinite alternate;
  will-change: box-shadow;
}

/* Staggered animations for lists */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Optimized Keyframes - Using transform3d for hardware acceleration */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translate3d(-30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale3d(0.9, 0.9, 1);
  }
  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale3d(0.5, 0.5, 1);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.02, 1.02, 1);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -8px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.03, 1.03, 1);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 15px rgba(14, 165, 233, 0.2);
  }
  to {
    box-shadow: 0 0 25px rgba(14, 165, 233, 0.4);
  }
}

/* 滚动动画增强 - 性能优化 */
.animate-in {
  opacity: 1 !important;
  transform: translate3d(0, 0, 0) scale3d(1, 1, 1) !important;
  transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: auto;
}

/* 视差效果 */
.parallax {
  will-change: transform;
}

/* 性能优化 - 减少动画对低性能设备的影响 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .float,
  .pulse,
  .glow {
    animation: none !important;
  }
}

/* 低性能设备优化 */
@media (max-width: 768px) {
  .float,
  .pulse {
    animation-duration: 6s;
  }

  .glow {
    animation: none;
  }
}

/* 自定义鼠标光标 */
.custom-cursor {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
}

.cursor-dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: -4px;
  left: -4px;
}

.cursor-ring {
  width: 40px;
  height: 40px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: -20px;
  left: -20px;
  transition: all 0.3s ease;
}

.cursor-hover .cursor-ring {
  transform: scale(1.5);
  border-color: var(--accent-color);
}

/* 增强的悬停效果 */
.enhanced-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.enhanced-hover:hover::before {
  left: 100%;
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--bg-tertiary);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
