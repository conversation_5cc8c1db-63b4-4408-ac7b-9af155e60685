<template>
  <section id="hero" class="hero-section">
    <div class="container">
      <div class="hero-content">
        <!-- 个人介绍 -->
        <div class="personal-intro fade-in">
          <div class="avatar-section scale-in float">
            <div class="avatar-ring"></div>
            <div class="avatar-container">
              <img
                :src="contentStore.personalInfo.avatar"
                :alt="contentStore.personalInfo.name + ' 头像'"
                class="avatar-img"
                @error="handleImageError"
              />
            </div>
          </div>

          <div class="intro-text">
            <div class="greeting slide-in-left stagger-1">
              {{ contentStore.personalInfo.greeting }}
            </div>

            <h1 class="name-title slide-up stagger-2">
              {{ contentStore.personalInfo.name }}
            </h1>

            <p class="description fade-in stagger-3">
              {{ contentStore.personalInfo.description }}
            </p>

            <div class="motto slide-up stagger-4">
              {{ contentStore.personalInfo.motto }}
            </div>

            <div class="contact-buttons slide-up stagger-5">
              <button @click="handleContactClick" class="btn btn-primary pulse">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
                联系我
              </button>
              <a href="https://github.com/Greatgrap" target="_blank" class="btn btn-secondary">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                GitHub
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { useContentStore } from '@/stores/content'

const contentStore = useContentStore()

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
  const parent = event.target.parentElement
  if (!parent.querySelector('.default-avatar')) {
    const defaultAvatar = document.createElement('div')
    defaultAvatar.className = 'default-avatar'
    defaultAvatar.textContent = '👨‍💻'
    parent.appendChild(defaultAvatar)
  }
}

// 处理联系我点击事件
const handleContactClick = () => {
  const email = contentStore.personalInfo.email
  alert(`请发邮件到：${email} ，谢谢`)
}
</script>

<style scoped>
.hero-section {
  min-height: 80vh;
  display: flex;
  align-items: center;
  padding: 6rem 0 4rem;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  width: 100%;
  align-items: center;
}

/* 个人介绍 */
.personal-intro {
  text-align: center;
}

.avatar-section {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto 2rem;
}

.avatar-ring {
  position: absolute;
  inset: 0;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: rotate 30s linear infinite;
  will-change: transform;
}

.avatar-ring::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border-radius: 50%;
}

.avatar-container {
  position: absolute;
  inset: 10px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.default-avatar {
  font-size: 3rem;
  color: white;
}

.greeting {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-weight: 500;
}

.name-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
}

.description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 500px;
  margin: 0 auto 1.5rem;
  line-height: 1.6;
}

.motto {
  font-size: 1.1rem;
  color: var(--text-muted);
  font-style: italic;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: 12px;
}

.contact-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-icon {
  width: 18px;
  height: 18px;
  margin-right: 0.5rem;
}





/* 动画 - 硬件加速优化 */
@keyframes rotate {
  from { transform: rotate3d(0, 0, 1, 0deg); }
  to { transform: rotate3d(0, 0, 1, 360deg); }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .personal-intro {
    text-align: left;
  }

  .avatar-section {
    margin: 0 0 2rem 0;
  }

  .description {
    margin: 0 0 1.5rem 0;
  }

  .contact-buttons {
    justify-content: flex-start;
  }
}

@media (min-width: 1024px) {
  .hero-section {
    min-height: 70vh;
    padding: 4rem 0 2rem;
  }

  .hero-content {
    max-width: 1000px;
    margin: 0 auto;
  }

  .avatar-section {
    width: 180px;
    height: 180px;
  }

  .personal-intro {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 4rem;
    align-items: center;
    text-align: left;
  }

  .avatar-section {
    margin: 0;
  }

  .description {
    margin: 0 0 1.5rem 0;
  }

  .contact-buttons {
    justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .hero-section {
    padding: 5rem 0 2rem;
  }

  .contact-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }
}
</style>
