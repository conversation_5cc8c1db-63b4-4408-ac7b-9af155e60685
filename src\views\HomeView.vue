<script setup>
import { onMounted } from 'vue'
import HeroSection from '../components/HeroSection.vue'
import ServicesSection from '../components/ServicesSection.vue'
import TechSection from '../components/TechSection.vue'
</script>

<template>
  <main class="home-page">
    <!-- 个人介绍 -->
    <HeroSection />

    <!-- 项目和技能展示 -->
    <div class="content-grid">
      <div class="container">
        <div class="grid-layout">
          <!-- 我的项目 -->
          <div class="projects-container">
            <ServicesSection />
          </div>

          <!-- 技能展示 -->
          <div class="skills-container">
            <TechSection />
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped>
.home-page {
  padding-top: 80px;
}

.content-grid {
  padding: 0;
}

.grid-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0;
}

/* 大屏布局优化 */
@media (min-width: 1024px) {
  .home-page {
    padding-top: 90px;
  }

  .grid-layout {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .projects-container,
  .skills-container {
    height: 100%;
  }
}

@media (min-width: 1440px) {
  .grid-layout {
    gap: 4rem;
  }
}
</style>
